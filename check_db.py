#!/usr/bin/env python3
"""
查看VS Code数据库中的Augment相关条目
"""

import os
import sys
import sqlite3
import platform
from pathlib import Path

def get_vscode_db_path():
    """获取VS Code数据库路径"""
    system = platform.system()
    
    if system == "Windows":
        appdata = os.environ.get("APPDATA")
        if not appdata:
            print("❌ APPDATA环境变量未找到")
            return None
        base_dir = Path(appdata) / "Code" / "User"
    elif system == "Darwin":  # macOS
        base_dir = Path.home() / "Library" / "Application Support" / "Code" / "User"
    elif system == "Linux":
        base_dir = Path.home() / ".config" / "Code" / "User"
    else:
        print(f"❌ 不支持的操作系统: {system}")
        return None
    
    return base_dir / "globalStorage" / "state.vscdb"

def check_augment_entries():
    """检查数据库中的Augment相关条目"""
    db_path = get_vscode_db_path()
    
    if not db_path or not db_path.exists():
        print(f"❌ VS Code数据库未找到: {db_path}")
        return
    
    print(f"📍 数据库位置: {db_path}")
    print("=" * 60)
    
    try:
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        # 查询所有包含"augment"的条目（不区分大小写）
        print("🔍 查询包含'augment'的条目...")
        cursor.execute("SELECT key, value FROM ItemTable WHERE LOWER(key) LIKE '%augment%'")
        augment_entries = cursor.fetchall()
        
        if augment_entries:
            print(f"📊 找到 {len(augment_entries)} 个Augment相关条目:")
            print("-" * 60)
            for i, (key, value) in enumerate(augment_entries, 1):
                print(f"{i}. Key: {key}")
                print(f"   Value: {value[:100]}{'...' if len(value) > 100 else ''}")
                print()
        else:
            print("✅ 没有找到包含'augment'的条目")
        
        # 查询总条目数
        cursor.execute("SELECT COUNT(*) FROM ItemTable")
        total_count = cursor.fetchone()[0]
        print(f"📈 数据库总条目数: {total_count}")
        
        # 查询一些示例条目（前5个）
        print("\n📋 数据库中的示例条目（前5个）:")
        print("-" * 60)
        cursor.execute("SELECT key, value FROM ItemTable LIMIT 5")
        sample_entries = cursor.fetchall()
        
        for i, (key, value) in enumerate(sample_entries, 1):
            print(f"{i}. Key: {key}")
            print(f"   Value: {value[:50]}{'...' if len(value) > 50 else ''}")
            print()
        
        conn.close()
        
    except sqlite3.Error as e:
        print(f"❌ SQLite错误: {e}")
    except Exception as e:
        print(f"❌ 意外错误: {e}")

if __name__ == "__main__":
    print("🔍 VS Code数据库Augment条目检查工具")
    print("=" * 60)
    check_augment_entries()
